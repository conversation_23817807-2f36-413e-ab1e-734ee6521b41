================================================================================
                        MONADFAAS QUICK REFERENCE CARD
                          Essential Commands for Presentation
================================================================================

🚀 SYSTEM STARTUP (3 terminals needed)
================================================================================

Terminal 1 - Blockchain:
cd MonadFaas/contracts && anvil

Terminal 2 - Dashboard:
cd MonadFaas/dashboard && npm run dev

Terminal 3 - Commands:
cd MonadFaas/contracts
forge script script/DeployFunctionRegistry.s.sol:DeployFunctionRegistry --rpc-url http://localhost:8545 --broadcast

📱 METAMASK SETUP
================================================================================
Network: Anvil Local
RPC: http://localhost:8545
Chain ID: 31337
Private Key: 0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80

🔧 CLI DEMO COMMANDS
================================================================================
cd MonadFaas/cli
npm run dev -- init demo-function
cd demo-function
# Edit monad-faas.config.json with local settings
cd .. && npm run dev -- build
cd demo-function && ../node_modules/.bin/ts-node ../src/index.ts register
../node_modules/.bin/ts-node ../src/index.ts status

🌐 WEB DASHBOARD
================================================================================
URL: http://localhost:3000
1. Connect Wallet
2. Create Function → Price Alert Template
3. Deploy and Monitor

⚡ SCALABILITY DEMO
================================================================================
cd MonadFaas
node demo-script.js
node verify-demo.js

🔍 VERIFICATION COMMANDS
================================================================================
# Function count
cast call ****************************************** "nextFunctionId()" --rpc-url http://localhost:8545

# Check function details
cast call ****************************************** "functions(uint256)" 42 --rpc-url http://localhost:8545

# Decode name
cast --to-ascii 0x70726963652d616c6572742d3230

# Current block
cast block-number --rpc-url http://localhost:8545

🆘 EMERGENCY FIXES
================================================================================
# Restart Anvil
pkill anvil && cd MonadFaas/contracts && anvil

# Restart Dashboard
cd MonadFaas/dashboard && rm -rf .next && npm run dev

# Check system health
curl http://localhost:8545 && curl http://localhost:3000

📊 SUCCESS METRICS TO HIGHLIGHT
================================================================================
✅ 42+ functions deployed on blockchain
✅ 100% success rate for core functionality  
✅ Sub-second registration times
✅ Real IPFS integration
✅ Complete Web3 stack
✅ Enterprise-scale parallel execution

🎯 KEY TALKING POINTS
================================================================================
- "Real blockchain transactions, not simulated"
- "Production-ready smart contracts"
- "Developer-friendly tools"
- "Decentralized and trustless"
- "Enterprise-scale serverless platform"

📋 DEMO CHECKLIST
================================================================================
□ Anvil running (Terminal 1)
□ Dashboard running (Terminal 2) 
□ Contracts deployed
□ MetaMask configured
□ Browser tabs open
□ CLI ready (Terminal 3)
□ Demo script ready
□ Verification commands tested

================================================================================
